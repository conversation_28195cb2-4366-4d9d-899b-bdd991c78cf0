<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goupload

```go
import "github.com/real-rm/goupload"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func AbortChunkedUpload\(ctx context.Context, uploadID string\) error](<#AbortChunkedUpload>)
- [func CleanupTestConfig\(t \*testing.T\)](<#CleanupTestConfig>)
- [func ExecuteWithRetry\(operation func\(\) error, maxRetries int, delay time.Duration\) error](<#ExecuteWithRetry>)
- [func GenerateFilename\(originalFilename string\) \(string, error\)](<#GenerateFilename>)
- [func GeneratePathSeed\(filename string, timestamp int64, uid string\) string](<#GeneratePathSeed>)
- [func InitiateChunkedUpload\(ctx context.Context, provider PathAndConfigProvider, site, entryName, uid, originalFilename string, totalFileSize int64\) \(string, error\)](<#InitiateChunkedUpload>)
- [func SetupTestConfig\(t \*testing.T\)](<#SetupTestConfig>)
- [func UploadChunk\(ctx context.Context, uploadID string, chunkNumber int, reader io.Reader\) error](<#UploadChunk>)
- [func ValidateFilename\(filename string\) error](<#ValidateFilename>)
- [type ChunkedUploadState](<#ChunkedUploadState>)
- [type DeleteResult](<#DeleteResult>)
  - [func Delete\(ctx context.Context, statsUpdater StatsUpdater, site, entryName, relativePath string\) \(\*DeleteResult, error\)](<#Delete>)
- [type DeletedPath](<#DeletedPath>)
- [type DirectoryFileEntry](<#DirectoryFileEntry>)
- [type DirectoryFileError](<#DirectoryFileError>)
- [type DirectoryUploadRequest](<#DirectoryUploadRequest>)
- [type DirectoryUploadResult](<#DirectoryUploadResult>)
  - [func UploadDirectory\(ctx context.Context, statsUpdater StatsUpdater, request \*DirectoryUploadRequest\) \(\*DirectoryUploadResult, error\)](<#UploadDirectory>)
- [type FailedPath](<#FailedPath>)
- [type FileInfo](<#FileInfo>)
- [type LevelStoreProvider](<#LevelStoreProvider>)
  - [func \(p \*LevelStoreProvider\) GenerateFilename\(originalFilename string\) \(string, error\)](<#LevelStoreProvider.GenerateFilename>)
  - [func \(p \*LevelStoreProvider\) GetPathComponents\(t time.Time, site, seed string\) \(\*levelStore.PathResult, error\)](<#LevelStoreProvider.GetPathComponents>)
  - [func \(p \*LevelStoreProvider\) GetUserUploadConfig\(site, entryName string\) \(\*levelStore.UserUploadConfig, error\)](<#LevelStoreProvider.GetUserUploadConfig>)
- [type PathAndConfigProvider](<#PathAndConfigProvider>)
- [type ProcessedDirectoryFile](<#ProcessedDirectoryFile>)
- [type S3ObjectMetadata](<#S3ObjectMetadata>)
  - [func \(m \*S3ObjectMetadata\) FromS3Headers\(headers map\[string\]string\)](<#S3ObjectMetadata.FromS3Headers>)
  - [func \(m \*S3ObjectMetadata\) ToS3Headers\(\) map\[string\]string](<#S3ObjectMetadata.ToS3Headers>)
- [type StatsUpdater](<#StatsUpdater>)
  - [func NewStatsUpdater\(site string, entryName string, coll \*gomongo.MongoCollection\) \(StatsUpdater, error\)](<#NewStatsUpdater>)
- [type UploadResult](<#UploadResult>)
  - [func CompleteChunkedUpload\(ctx context.Context, statsUpdater StatsUpdater, uploadID string, expectedChunks int, s3ProviderMap map\[string\]levelStore.S3ProviderConfig\) \(\*UploadResult, error\)](<#CompleteChunkedUpload>)
  - [func Upload\(ctx context.Context, statsUpdater StatsUpdater, site, entryName, uid string, reader io.Reader, originalFilename string, clientDeclaredSize int64\) \(\*UploadResult, error\)](<#Upload>)
- [type WriteOptions](<#WriteOptions>)
  - [func DefaultWriteOptions\(\) WriteOptions](<#DefaultWriteOptions>)
- [type WrittenPath](<#WrittenPath>)
  - [func WriteToLocationsWithRollback\(ctx context.Context, config \*levelStore.UserUploadConfig, s3ProviderMap map\[string\]levelStore.S3ProviderConfig, reader io.ReadSeeker, relativePath string, opts WriteOptions, metadata map\[string\]string, fileInfo \*FileInfo\) \(\[\]WrittenPath, error\)](<#WriteToLocationsWithRollback>)


## Constants

<a name="SINGLE_FILE_MAX_LIMIT"></a>

```go
const (
    // SINGLE_FILE_MAX_LIMIT 为普通单文件上传定义了最大大小。
    SINGLE_FILE_MAX_LIMIT int64 = 500 * 1024 * 1024 // 500 MB
    // CHUNKED_UPLOAD_MAX_LIMIT 为分块上传定义了最大总大小。
    CHUNKED_UPLOAD_MAX_LIMIT int64 = 10 * 1024 * 1024 * 1024 // 10 GB
    // MIME_DETECTION_BUFFER_SIZE 定义了用于MIME类型检测的缓冲区大小。
    MIME_DETECTION_BUFFER_SIZE = 512
)
```

<a name="MAX_CHUNKS_PER_UPLOAD"></a>

```go
const (

    // MAX_CHUNKS_PER_UPLOAD 定义了单个上传任务允许的最大分块数量，以防止DoS攻击。
    // 这个值参考了AWS S3的默认限制。
    MAX_CHUNKS_PER_UPLOAD = 10000
)
```

## Variables

<a name="ErrFileTooLarge"></a>ErrFileTooLarge 表示文件大小超过允许的最大限制

```go
var ErrFileTooLarge = errors.New("file size exceeds the maximum allowed limit")
```

<a name="AbortChunkedUpload"></a>
## func [AbortChunkedUpload](<https://github.com/real-rm/goupload/blob/main/upload_chunked.go#L377>)

```go
func AbortChunkedUpload(ctx context.Context, uploadID string) error
```

AbortChunkedUpload 中止一个正在进行的分块上传并清理所有相关资源。 如果uploadID无效或已完成，此操作将静默失败。

<a name="CleanupTestConfig"></a>
## func [CleanupTestConfig](<https://github.com/real-rm/goupload/blob/main/test_setup.go#L85>)

```go
func CleanupTestConfig(t *testing.T)
```

CleanupTestConfig 清理测试配置（可选）

<a name="ExecuteWithRetry"></a>
## func [ExecuteWithRetry](<https://github.com/real-rm/goupload/blob/main/upload_helper.go#L75>)

```go
func ExecuteWithRetry(operation func() error, maxRetries int, delay time.Duration) error
```

ExecuteWithRetry 执行带重试的操作

<a name="GenerateFilename"></a>
## func [GenerateFilename](<https://github.com/real-rm/goupload/blob/main/upload_helper.go#L17>)

```go
func GenerateFilename(originalFilename string) (string, error)
```

GenerateFilename 生成唯一的文件名，保留原始文件扩展名 如果无法生成唯一的ID，它将返回一个错误。

<a name="GeneratePathSeed"></a>
## func [GeneratePathSeed](<https://github.com/real-rm/goupload/blob/main/upload_helper.go#L96>)

```go
func GeneratePathSeed(filename string, timestamp int64, uid string) string
```

GeneratePathSeed 生成路径计算的种子字符串

<a name="InitiateChunkedUpload"></a>
## func [InitiateChunkedUpload](<https://github.com/real-rm/goupload/blob/main/upload_chunked.go#L133>)

```go
func InitiateChunkedUpload(ctx context.Context, provider PathAndConfigProvider, site, entryName, uid, originalFilename string, totalFileSize int64) (string, error)
```

InitiateChunkedUpload initializes a new chunked upload task. It validates parameters, prepares storage paths and metadata, and returns a unique upload ID. totalFileSize is the client\-declared total file size, used for early size validation. It now accepts a PathAndConfigProvider for better testability.

<a name="SetupTestConfig"></a>
## func [SetupTestConfig](<https://github.com/real-rm/goupload/blob/main/test_setup.go#L17>)

```go
func SetupTestConfig(t *testing.T)
```

SetupTestConfig 设置测试配置环境 这个函数应该在所有需要配置的测试开始前调用

<a name="UploadChunk"></a>
## func [UploadChunk](<https://github.com/real-rm/goupload/blob/main/upload_chunked.go#L296>)

```go
func UploadChunk(ctx context.Context, uploadID string, chunkNumber int, reader io.Reader) error
```

UploadChunk 接收并保存一个文件分块。

<a name="ValidateFilename"></a>
## func [ValidateFilename](<https://github.com/real-rm/goupload/blob/main/upload_helper.go#L37>)

```go
func ValidateFilename(filename string) error
```

ValidateFilename 验证文件名是否合法

<a name="ChunkedUploadState"></a>
## type [ChunkedUploadState](<https://github.com/real-rm/goupload/blob/main/upload_chunked.go#L29-L48>)

ChunkedUploadState 存储一个分块上传会话的元数据和进度。

```go
type ChunkedUploadState struct {
    UploadID         string
    Site             string
    EntryName        string
    UID              string
    OriginalFilename string
    TotalFileSize    int64 // 客户端声明的总文件大小

    // 从 prepareUploadPrerequisites 获取的预处理数据
    Prerequisites *uploadPrerequisites

    // 运行时状态
    TempChunkDir       string       // 存放此任务所有分块的临时目录
    UploadedChunks     map[int]bool // 记录已成功上传的块编号
    TotalBytesReceived int64        // 已接收的总字节数
    CreatedAt          time.Time    // 用于实现过期清理
    // contains filtered or unexported fields
}
```

<a name="DeleteResult"></a>
## type [DeleteResult](<https://github.com/real-rm/goupload/blob/main/delete_core.go#L15-L20>)

DeleteResult 表示删除操作的结果

```go
type DeleteResult struct {
    Path            string        `json:"path"`
    DeletedPaths    []DeletedPath `json:"deleted_paths"`
    FailedPaths     []FailedPath  `json:"failed_paths,omitempty"`
    IsPartialDelete bool          `json:"is_partial_delete"`
}
```

<a name="Delete"></a>
### func [Delete](<https://github.com/real-rm/goupload/blob/main/delete_core.go#L40>)

```go
func Delete(ctx context.Context, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error)
```

Delete 高级API函数，提供完整的文件删除流程

<a name="DeletedPath"></a>
## type [DeletedPath](<https://github.com/real-rm/goupload/blob/main/delete_core.go#L23-L29>)

DeletedPath 表示已删除的路径信息

```go
type DeletedPath struct {
    Type     string `json:"type"` // "local" 或 "s3"
    Path     string `json:"path"`
    Target   string `json:"target,omitempty"`   // S3目标名称
    Bucket   string `json:"bucket,omitempty"`   // S3 bucket
    Endpoint string `json:"endpoint,omitempty"` // S3端点
}
```

<a name="DirectoryFileEntry"></a>
## type [DirectoryFileEntry](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L26-L31>)

DirectoryFileEntry 目录文件条目

```go
type DirectoryFileEntry struct {
    RelativePath string     // 文件在目录中的相对路径，如 "subdir/file.txt"
    Reader       io.Reader  // 文件内容
    Size         int64      // 文件大小
    ModTime      *time.Time // 文件修改时间（可选）
}
```

<a name="DirectoryFileError"></a>
## type [DirectoryFileError](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L45-L48>)

DirectoryFileError 文件上传错误

```go
type DirectoryFileError struct {
    RelativePath string // 文件相对路径
    Error        string // 错误信息
}
```

<a name="DirectoryUploadRequest"></a>
## type [DirectoryUploadRequest](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L16-L23>)

DirectoryUploadRequest 目录上传请求

```go
type DirectoryUploadRequest struct {
    Site          string               // 板块名称
    EntryName     string               // 入口类型
    UserID        string               // 用户ID
    DirectoryName string               // 目录名称
    Files         []DirectoryFileEntry // 文件列表
    MaxSize       int64                // 目录总大小限制（可选，0表示使用配置默认值）
}
```

<a name="DirectoryUploadResult"></a>
## type [DirectoryUploadResult](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L34-L42>)

DirectoryUploadResult 目录上传结果

```go
type DirectoryUploadResult struct {
    DirectoryPath   string               // 目录在存储中的路径
    TotalFiles      int                  // 总文件数
    TotalSize       int64                // 总大小
    SuccessFiles    []string             // 成功上传的文件相对路径
    FailedFiles     []DirectoryFileError // 失败的文件
    WrittenPaths    []WrittenPath        // 所有写入的存储路径
    IsPartialUpload bool                 // 是否部分上传成功
}
```

<a name="UploadDirectory"></a>
### func [UploadDirectory](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L60-L64>)

```go
func UploadDirectory(ctx context.Context, statsUpdater StatsUpdater, request *DirectoryUploadRequest) (*DirectoryUploadResult, error)
```

UploadDirectory 上传目录

<a name="FailedPath"></a>
## type [FailedPath](<https://github.com/real-rm/goupload/blob/main/delete_core.go#L32-L37>)

FailedPath 表示删除失败的路径信息

```go
type FailedPath struct {
    Type    string `json:"type"`
    Path    string `json:"path"`
    Target  string `json:"target,omitempty"`
    Message string `json:"message"`
}
```

<a name="FileInfo"></a>
## type [FileInfo](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L794-L797>)

FileInfo 包含了文件的基本元数据

```go
type FileInfo struct {
    Size     int64  `json:"size"`      // 文件大小
    MimeType string `json:"mime_type"` // MIME类型
}
```

<a name="LevelStoreProvider"></a>
## type [LevelStoreProvider](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L230>)

LevelStoreProvider 是 PathAndConfigProvider 接口的生产环境实现。 它直接调用 levelStore 包中的函数。

```go
type LevelStoreProvider struct{}
```

<a name="LevelStoreProvider.GenerateFilename"></a>
### func \(\*LevelStoreProvider\) [GenerateFilename](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L243>)

```go
func (p *LevelStoreProvider) GenerateFilename(originalFilename string) (string, error)
```

GenerateFilename 调用 goupload 包中的 GenerateFilename 函数。

<a name="LevelStoreProvider.GetPathComponents"></a>
### func \(\*LevelStoreProvider\) [GetPathComponents](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L238>)

```go
func (p *LevelStoreProvider) GetPathComponents(t time.Time, site, seed string) (*levelStore.PathResult, error)
```

GetPathComponents 调用 levelStore 来获取路径组件。

<a name="LevelStoreProvider.GetUserUploadConfig"></a>
### func \(\*LevelStoreProvider\) [GetUserUploadConfig](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L233>)

```go
func (p *LevelStoreProvider) GetUserUploadConfig(site, entryName string) (*levelStore.UserUploadConfig, error)
```

GetUserUploadConfig 调用 levelStore 来获取用户上传配置。

<a name="PathAndConfigProvider"></a>
## type [PathAndConfigProvider](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L221-L226>)

PathAndConfigProvider 定义了获取配置和路径组件的依赖项接口。 这使得在测试中可以模拟\(mock\)这些依赖。

```go
type PathAndConfigProvider interface {
    GetUserUploadConfig(site, entryName string) (*levelStore.UserUploadConfig, error)
    GetPathComponents(t time.Time, site, seed string) (*levelStore.PathResult, error)
    GenerateFilename(originalFilename string) (string, error)
}
```

<a name="ProcessedDirectoryFile"></a>
## type [ProcessedDirectoryFile](<https://github.com/real-rm/goupload/blob/main/upload_directory.go#L51-L57>)

ProcessedDirectoryFile 预处理后的目录文件

```go
type ProcessedDirectoryFile struct {
    RelativePath string
    Reader       io.ReadSeeker // 统一转换为可重复读取
    FileInfo     *FileInfo
    ModTime      *time.Time
    CleanupFunc  func() // 清理函数（如果需要）
}
```

<a name="S3ObjectMetadata"></a>
## type [S3ObjectMetadata](<https://github.com/real-rm/goupload/blob/main/types.go#L51-L57>)

S3ObjectMetadata S3对象元数据

```go
type S3ObjectMetadata struct {
    OriginalName string `json:"onm"`  // 原始文件名
    Timestamp    int64  `json:"ts"`   // 上传时间戳
    MimeType     string `json:"mime"` // MIME类型
    UserID       string `json:"uid"`  // 用户ID
    Tags         string `json:"tags"` // 标签
}
```

<a name="S3ObjectMetadata.FromS3Headers"></a>
### func \(\*S3ObjectMetadata\) [FromS3Headers](<https://github.com/real-rm/goupload/blob/main/types.go#L83>)

```go
func (m *S3ObjectMetadata) FromS3Headers(headers map[string]string)
```

FromS3Headers 从S3头部创建元数据

<a name="S3ObjectMetadata.ToS3Headers"></a>
### func \(\*S3ObjectMetadata\) [ToS3Headers](<https://github.com/real-rm/goupload/blob/main/types.go#L60>)

```go
func (m *S3ObjectMetadata) ToS3Headers() map[string]string
```

ToS3Headers 将元数据转换为S3头部格式

<a name="StatsUpdater"></a>
## type [StatsUpdater](<https://github.com/real-rm/goupload/blob/main/types.go#L46-L48>)

StatsUpdater 定义了目录统计更新器的接口 这使得在测试中可以轻松地模拟\(mock\)DirKeyStore

```go
type StatsUpdater interface {
    AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int)
}
```

<a name="NewStatsUpdater"></a>
### func [NewStatsUpdater](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L800>)

```go
func NewStatsUpdater(site string, entryName string, coll *gomongo.MongoCollection) (StatsUpdater, error)
```

NewStatsUpdater 自动初始化并返回 StatsUpdater（DirKeyStore），并自动设置 customDirs

<a name="UploadResult"></a>
## type [UploadResult](<https://github.com/real-rm/goupload/blob/main/types.go#L11-L18>)

UploadResult 表示上传操作的结果

```go
type UploadResult struct {
    Path         string        `json:"path"`          // 完整的URL路径 (如: "/useruploads/1225/abc12/file.pdf")
    Prefix       string        `json:"prefix"`        // URL前缀 (如: "/useruploads")
    Size         int64         `json:"size"`          // 文件大小（字节）
    WrittenPaths []WrittenPath `json:"written_paths"` // 所有写入位置详情
    Filename     string        `json:"filename"`      // 生成的新文件名
    MimeType     string        `json:"mime_type"`     // 检测到的MIME类型
}
```

<a name="CompleteChunkedUpload"></a>
### func [CompleteChunkedUpload](<https://github.com/real-rm/goupload/blob/main/upload_chunked.go#L422>)

```go
func CompleteChunkedUpload(ctx context.Context, statsUpdater StatsUpdater, uploadID string, expectedChunks int, s3ProviderMap map[string]levelStore.S3ProviderConfig) (*UploadResult, error)
```

CompleteChunkedUpload finalizes a chunked upload. It verifies all chunks, combines them, and writes them to the final destinations. It now accepts an s3ProviderMap to make it fully testable.

<a name="Upload"></a>
### func [Upload](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L150>)

```go
func Upload(ctx context.Context, statsUpdater StatsUpdater, site, entryName, uid string, reader io.Reader, originalFilename string, clientDeclaredSize int64) (*UploadResult, error)
```

Upload 高级API函数，提供完整的文件上传流程 自动获取用户配置，处理 userupload 部分，提取 WriteOptions 配置，并使用传入的StatsUpdater更新统计

<a name="WriteOptions"></a>
## type [WriteOptions](<https://github.com/real-rm/goupload/blob/main/types.go#L32-L42>)

WriteOptions 表示写入操作的配置选项

```go
type WriteOptions struct {
    MaxRetries      int           `json:"max_retries"`      // 最大重试次数（默认: 3）
    RetryDelay      time.Duration `json:"retry_delay"`      // 重试延迟（默认: 1秒）
    S3Timeout       time.Duration `json:"s3_timeout"`       // S3操作超时（默认: 30秒）
    ChunkSize       int64         `json:"chunk_size"`       // S3分块上传大小（默认: 5MB）
    EnableLogging   bool          `json:"enable_logging"`   // 是否启用详细日志
    ValidateContent bool          `json:"validate_content"` // 是否验证文件内容（默认: true）
    EnableMetadata  bool          `json:"enable_metadata"`  // 是否启用S3元数据（默认: true）
    S3ACL           types.ObjectCannedACL
    ContentType     *string
}
```

<a name="DefaultWriteOptions"></a>
### func [DefaultWriteOptions](<https://github.com/real-rm/goupload/blob/main/types.go#L104>)

```go
func DefaultWriteOptions() WriteOptions
```

DefaultWriteOptions 返回默认的写入选项

<a name="WrittenPath"></a>
## type [WrittenPath](<https://github.com/real-rm/goupload/blob/main/types.go#L21-L29>)

WrittenPath 表示文件写入位置的详细信息

```go
type WrittenPath struct {
    Type     string            `json:"type"`             // 存储类型: "local" 或 "s3"
    Path     string            `json:"path"`             // 本地存储的完整物理路径 或 S3的key路径
    Size     int64             `json:"size"`             // 写入的文件大小
    Endpoint string            `json:"endpoint"`         // S3端点（仅S3类型）
    Bucket   string            `json:"bucket"`           // S3存储桶（仅S3类型）
    Target   string            `json:"target,omitempty"` // S3提供商名称 (仅S3类型)
    Metadata *S3ObjectMetadata `json:"metadata"`         // S3元数据（仅S3类型）
}
```

<a name="WriteToLocationsWithRollback"></a>
### func [WriteToLocationsWithRollback](<https://github.com/real-rm/goupload/blob/main/upload_core.go#L311>)

```go
func WriteToLocationsWithRollback(ctx context.Context, config *levelStore.UserUploadConfig, s3ProviderMap map[string]levelStore.S3ProviderConfig, reader io.ReadSeeker, relativePath string, opts WriteOptions, metadata map[string]string, fileInfo *FileInfo) ([]WrittenPath, error)
```

WriteToLocationsWithRollback 核心的事务性写入函数 适用于需要自定义路径或批量上传的场景

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
