package main

import (
	"context"
	"fmt"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// needToProcess determines whether a property needs to be processed based on disk and migration stage
func needToProcess(prop bson.M, disk string) bool {
	src, ok := prop["src"].(string)
	if !ok {
		return false
	}

	// For ca6 stage: Skip if already processed (has phoLH field)
	if disk == "ca6" {
		if prop["phoLH"] != nil {
			return false
		}
	}

	// For ca7 stage: Check if ca7 processing is already done by looking for _old fields
	if disk == "ca7" {
		if src == SRC_TRB || src == SRC_DDF {
			// If pho_old exists, ca7 processing is already done
			if prop["pho_old"] != nil {
				return false
			}
		} else if src == SRC_OTW || src == SRC_CLG {
			// If phoIDs_old exists, ca7 processing is already done
			if prop["phoIDs_old"] != nil {
				return false
			}
		}
	}

	// Common logic for both ca6 and ca7: check original fields
	// For TRB and DDF, check pho field
	if src == SRC_TRB || src == SRC_DDF {
		if pho, ok := prop["pho"]; ok {
			if phoInt, ok := pho.(int32); ok && phoInt > 0 {
				return true
			}
			if phoInt, ok := pho.(int); ok && phoInt > 0 {
				return true
			}
		}
	}

	// For OTW and CLG, check phoIDs field
	if src == SRC_OTW || src == SRC_CLG {
		if phoIDs, ok := prop["phoIDs"]; ok && phoIDs != nil {
			return true
		}
	}

	return false
}

// getPropertiesCursor gets a cursor for streaming properties that need to be processed
func getPropertiesCursor(config Config) (*mongo.Cursor, error) {
	collection := gomongo.Coll("listing", "properties")
	if collection == nil {
		return nil, fmt.Errorf("failed to get properties collection")
	}

	// Build query filter
	filter := bson.M{}
	if config.MT != nil {
		filter["mt"] = bson.M{"$gte": *config.MT}
	}

	// Build projection to include necessary fields
	projection := bson.M{
		"mt":     1,
		"sid":    1,
		"src":    1,
		"ts":     1,
		"board":  1,
		"pho":    1,
		"phoIDs": 1,
		"orgId":  1,
		"phoLH":  1,
		"onD":    1,
	}

	// Build query options with reverse chronological order
	opts := options.Find().
		SetProjection(projection).
		SetSort(bson.M{"ts": -1})

	ctx := context.Background()
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to query properties: %w", err)
	}

	golog.Info("Created properties cursor for streaming")
	return cursor, nil
}

// insertImmigrationLog inserts migration log into database
func insertImmigrationLog(result MigrationResult) error {
	var collectionName string
	if result.Disk == "ca6" {
		collectionName = "photo_immigration_ca6"
	} else {
		collectionName = "photo_immigration_ca7"
	}

	collection := gomongo.Coll("listing", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	ctx := context.Background()
	_, err := collection.InsertOne(ctx, result)
	if err != nil {
		return fmt.Errorf("failed to insert immigration log: %w", err)
	}

	return nil
}

// updatePropDB updates the properties collection with new phoHL and tnHL fields
func updatePropDB(propID string, phoHL []int32, tnHL int32) error {
	collection := gomongo.Coll("listing", "properties")
	if collection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	filter := bson.M{"_id": propID}
	update := bson.M{
		"$set": bson.M{
			"phoHL": phoHL,
			"tnHL":  tnHL,
		},
	}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update prop: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	golog.Info("Updated prop", "propID", propID, "phoHL", len(phoHL), "tnHL", tnHL)
	return nil
}

// updateRniDB updates the corresponding RNI collection with thumbnail hash
func updateRniDB(prop bson.M, tnHL int32) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var collectionName string
	var queryField string
	var queryValue interface{}

	// Determine collection and query parameters based on source type
	switch src {
	case SRC_TRB:
		collectionName = "mls_treb_master_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_DDF:
		collectionName = "mls_crea_ddf_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_OTW:
		collectionName = "mls_oreb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	case SRC_CLG:
		collectionName = "mls_creb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	filter := bson.M{queryField: queryValue}
	update := bson.M{
		"$set": bson.M{
			"tnHL": tnHL,
		},
	}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update rni: %w", err)
	}

	if result.MatchedCount == 0 {
		golog.Warn("No rni record found", "collection", collectionName, "queryField", queryField, "queryValue", queryValue)
	} else {
		golog.Info("Updated rni", "collection", collectionName, "queryValue", queryValue, "tnHL", tnHL)
	}

	return nil
}

// updateDirStats updates directory statistics for the levelstore
func updateDirStats(prop bson.M, l1, l2 string, entityCount, fileCount int) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var store *levelStore.DirKeyStore
	switch src {
	case SRC_TRB:
		store = storeTRB
	case SRC_DDF:
		store = storeDDF
	case SRC_OTW:
		store = storeOTW
	case SRC_CLG:
		store = storeCLG
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	if store == nil {
		return fmt.Errorf("store not initialized for src: %s", src)
	}

	store.AddDirStats(l1, l2, entityCount, fileCount)
	golog.Info("Updated dir stats", "src", src, "l1", l1, "l2", l2, "entities", entityCount, "files", fileCount)
	return nil
}

// updateDB updates both properties and RNI collections based on ca6/ca7 stage
func updateDB(params DBUpdateParams) error {
	propID, ok := params.Prop["_id"].(string)
	if !ok {
		return fmt.Errorf("invalid prop _id")
	}

	if params.Disk == "ca6" {
		// ca6 stage: Add new phoHL, tnHL, and phoP fields
		if err := updatePropDBCA6(propID, params.PhoHL, params.TnHL, params.PhoP); err != nil {
			return fmt.Errorf("failed to update prop DB for ca6: %w", err)
		}

		// Update RNI collection
		if err := updateRniDB(params.Prop, params.TnHL); err != nil {
			return fmt.Errorf("failed to update rni DB: %w", err)
		}

	} else if params.Disk == "ca7" {
		// ca7 stage: Rename old fields to _old
		if err := updatePropDBCA7(propID, params.Prop); err != nil {
			return fmt.Errorf("failed to update prop DB for ca7: %w", err)
		}

		// Update RNI collection for ca7 (rename old fields)
		if err := updateRniDBCA7(params.Prop); err != nil {
			return fmt.Errorf("failed to update rni DB for ca7: %w", err)
		}
	}

	return nil
}

// updatePropDBCA6 updates both properties and merged collections for ca6 stage
func updatePropDBCA6(propID string, phoHL []int32, tnHL int32, phoP string) error {

	updateFields := bson.M{
		"phoHL": phoHL,
		"tnHL":  tnHL,
		"phoP":  phoP,
	}

	ctx := context.Background()

	// Update properties collection
	propCollection := gomongo.Coll("listing", "properties")
	if propCollection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	propFilter := bson.M{"_id": propID}
	propUpdate := bson.M{"$set": updateFields}

	propResult, err := propCollection.UpdateOne(ctx, propFilter, propUpdate)
	if err != nil {
		return fmt.Errorf("failed to update prop: %w", err)
	}

	if propResult.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	// Update merged collection
	mergedCollection := gomongo.Coll("listing", "merged")
	if mergedCollection == nil {
		return fmt.Errorf("failed to get merged collection")
	}

	mergedFilter := bson.M{"_id": propID}
	mergedUpdate := bson.M{"$set": updateFields}

	mergedResult, err := mergedCollection.UpdateOne(ctx, mergedFilter, mergedUpdate)
	if err != nil {
		return fmt.Errorf("failed to update merged: %w", err)
	}

	if mergedResult.MatchedCount == 0 {
		golog.Info("No merged record found for update", "propID", propID)
	} else {
		golog.Info("Updated merged for ca6", "propID", propID, "phoHL", len(phoHL), "tnHL", tnHL, "phoP", phoP)
	}

	golog.Info("Updated prop for ca6", "propID", propID, "phoHL", len(phoHL), "tnHL", tnHL, "phoP", phoP)
	return nil
}

// updatePropDBCA7 updates both properties and merged collections for ca7 stage (rename old fields)
func updatePropDBCA7(propID string, prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	filter := bson.M{"_id": propID}
	update := bson.M{}

	// Rename fields based on source type
	if src == SRC_TRB || src == SRC_DDF {
		// Rename pho to pho_old
		if pho := prop["pho"]; pho != nil {
			update["$set"] = bson.M{"pho_old": pho}
			update["$unset"] = bson.M{"pho": ""}
		}
	} else if src == SRC_OTW || src == SRC_CLG {
		// Rename phoIDs to phoIDs_old
		if phoIDs := prop["phoIDs"]; phoIDs != nil {
			update["$set"] = bson.M{"phoIDs_old": phoIDs}
			update["$unset"] = bson.M{"phoIDs": ""}
		}
	}

	if len(update) == 0 {
		golog.Info("No fields to rename for ca7", "propID", propID, "src", src)
		return nil
	}

	ctx := context.Background()

	// Update properties collection
	propCollection := gomongo.Coll("listing", "properties")
	if propCollection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	propResult, err := propCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update prop for ca7: %w", err)
	}

	if propResult.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	// Update merged collection
	mergedCollection := gomongo.Coll("listing", "merged")
	if mergedCollection == nil {
		return fmt.Errorf("failed to get merged collection")
	}

	mergedResult, err := mergedCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update merged for ca7: %w", err)
	}

	if mergedResult.MatchedCount == 0 {
		golog.Info("No merged record found for ca7 update", "propID", propID)
	} else {
		golog.Info("Updated merged for ca7", "propID", propID, "src", src)
	}

	golog.Info("Updated prop for ca7", "propID", propID, "src", src)
	return nil
}

// updateRniDBCA7 updates the RNI collection for ca7 stage (rename old fields)
func updateRniDBCA7(prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var collectionName string
	var queryField string
	var queryValue interface{}

	// Determine collection and query parameters based on source type
	switch src {
	case SRC_TRB:
		collectionName = "mls_treb_master_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_DDF:
		collectionName = "mls_crea_ddf_records"
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_OTW:
		collectionName = "mls_oreb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	case SRC_CLG:
		collectionName = "mls_creb_master_records"
		queryField = "_id"
		queryValue = prop["orgId"]
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	filter := bson.M{queryField: queryValue}
	update := bson.M{}

	// Rename fields based on source type
	if src == SRC_TRB || src == SRC_DDF {
		// For TRB/DDF, rename pho to pho_old if it exists
		update["$rename"] = bson.M{"pho": "pho_old"}
	} else if src == SRC_OTW || src == SRC_CLG {
		// For OTW/CLG, rename phoIDs to phoIDs_old if it exists
		update["$rename"] = bson.M{"phoIDs": "phoIDs_old"}
	}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update rni for ca7: %w", err)
	}

	if result.MatchedCount == 0 {
		golog.Info("No rni record found for ca7 update", "collection", collectionName, "queryValue", queryValue)
	} else {
		golog.Info("Updated rni for ca7", "collection", collectionName, "queryValue", queryValue)
	}

	return nil
}
