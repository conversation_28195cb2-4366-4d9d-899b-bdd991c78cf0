package main

import (
	"fmt"
	"image"
	_ "image/jpeg" // Support JPEG decoding
	_ "image/png"  // Support PNG decoding
	"os"
	"path/filepath"

	gofile "github.com/real-rm/gofile"
	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/image/draw"
)

// fileExists checks if a file exists at the given path
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ensureDir ensures that the directory for the given file path exists
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}
	return nil
}

// renameFile renames a file from source to destination
func renameFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	if err := os.Rename(src, dst); err != nil {
		return fmt.Errorf("failed to rename %s to %s: %w", src, dst, err)
	}

	golog.Info("File renamed successfully", "src", src, "dst", dst)
	return nil
}

// copyFile copies a file from source to destination
func copyFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer srcFile.Close()

	// Create destination file
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer dstFile.Close()

	// Copy content
	if _, err := srcFile.WriteTo(dstFile); err != nil {
		return fmt.Errorf("failed to copy content from %s to %s: %w", src, dst, err)
	}

	golog.Info("File copied successfully", "src", src, "dst", dst)
	return nil
}

// renameOrCopy attempts to rename file, falls back to copy if rename fails
// This implements the ca6/ca7 fallback strategy as specified in the migration document
func renameOrCopy(srcPath, dstPath, disk string) (string, error) {
	golog.Info("Starting file operation", "src", srcPath, "dst", dstPath, "disk", disk)

	// Check if source file exists on current disk
	if !fileExists(srcPath) {
		golog.Error("Source file not found on current disk", "path", srcPath, "disk", disk)

		// If ca6 disk and source file not found, try to copy from ca7 as fallback
		if disk == "ca6" {
			return copyFromCA7Fallback(srcPath, dstPath)
		}

		// For ca7 disk, if file not found, return error directly
		return STATUS_NOT_FOUND, fmt.Errorf("file not found on %s: %s", disk, srcPath)
	}

	// First attempt to rename (move within same disk)
	golog.Info("Attempting to rename file", "src", srcPath, "dst", dstPath)
	if err := renameFile(srcPath, dstPath); err == nil {
		golog.Info("File renamed successfully", "src", srcPath, "dst", dstPath)
		return STATUS_RENAMED, nil
	} else {
		golog.Error("Rename failed", "src", srcPath, "dst", dstPath, "error", err)

		// If ca6 disk and rename failed, try to copy from ca7 as fallback
		if disk == "ca6" {
			golog.Info("Rename failed on ca6, trying ca7 fallback", "src", srcPath)
			return copyFromCA7Fallback(srcPath, dstPath)
		}

		// For ca7 disk, if rename fails, return error directly
		return STATUS_RENAME_FAILED, fmt.Errorf("rename failed on %s: %w", disk, err)
	}
}

// copyFromCA7Fallback attempts to copy file from ca7 when ca6 operation fails
func copyFromCA7Fallback(originalSrcPath, dstPath string) (string, error) {
	// Construct ca7 path by replacing ca6 prefix with ca7
	ca7SrcPath := originalSrcPath
	if len(originalSrcPath) >= len(OLD_CA6_PATH) && originalSrcPath[:len(OLD_CA6_PATH)] == OLD_CA6_PATH {
		ca7SrcPath = OLD_CA7_PATH + originalSrcPath[len(OLD_CA6_PATH):]
	}

	golog.Info("Attempting ca7 fallback copy", "ca7_src", ca7SrcPath, "dst", dstPath)

	// Check if file exists on ca7
	if !fileExists(ca7SrcPath) {
		golog.Error("File not found on ca7 fallback", "path", ca7SrcPath)
		return STATUS_NOT_FOUND, fmt.Errorf("file not found on ca7 fallback: %s", ca7SrcPath)
	}

	// Attempt to copy from ca7
	if err := copyFile(ca7SrcPath, dstPath); err != nil {
		golog.Error("Ca7 fallback copy failed", "ca7_src", ca7SrcPath, "dst", dstPath, "error", err)
		return STATUS_COPY_FAILED, fmt.Errorf("ca7 fallback copy failed: %w", err)
	}

	golog.Info("Ca7 fallback copy successful", "ca7_src", ca7SrcPath, "dst", dstPath)
	return STATUS_COPIED, nil
}

// createThumbnail creates a thumbnail from the original image using predetermined hash
func createThumbnail(originalPath, thumbnailPath string, thumbnailHash int32) error {
	// Check if original file exists
	if !fileExists(originalPath) {
		return fmt.Errorf("original file not found: %s", originalPath)
	}

	// Load and resize local image file
	resizedImg, err := loadAndResizeLocalImage(originalPath, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return fmt.Errorf("failed to load and resize image: %w", err)
	}

	// Ensure thumbnail directory exists
	if err := ensureDir(thumbnailPath); err != nil {
		return err
	}

	// Save thumbnail as JPEG
	savedPath, err := gofile.SaveImage(resizedImg, thumbnailPath, false) // false = JPEG format
	if err != nil {
		return fmt.Errorf("failed to save thumbnail: %w", err)
	}

	golog.Info("Thumbnail created", "original", originalPath, "thumbnail", savedPath, "hash", thumbnailHash)
	return nil
}

// processImageFileWithHash processes a single image file with predetermined hash
func processImageFileWithHash(prop bson.M, relativePath string, hash int32, config Config) (*ImageInfo, error) {
	// Build full source path
	var basePath string
	if config.Disk == "ca6" {
		basePath = OLD_CA6_PATH
	} else {
		basePath = OLD_CA7_PATH
	}
	srcPath := filepath.Join(basePath, relativePath)
	golog.Debug("Built original path with custom hash", "srcPath", srcPath, "hash", hash)

	// Extract sid
	sid, ok := prop["sid"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid sid field")
	}

	// Generate base path
	onD, ok := prop["onD"].(int)
	if !ok {
		return nil, fmt.Errorf("invalid onD field")
	}
	ts := gohelper.DateToTime(onD)

	src, ok := prop["src"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid src field")
	}

	basePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		return nil, fmt.Errorf("failed to get file path: %w", err)
	}

	// Build new path using predetermined hash
	newPath, err := buildNewPathWithHash(basePath, sid, hash)
	if err != nil {
		return nil, fmt.Errorf("failed to build new path with hash: %w", err)
	}

	// Build full destination path
	var newBasePath string
	if config.Disk == "ca6" {
		newBasePath = NEW_CA6_PATH
	} else {
		newBasePath = NEW_CA7_PATH
	}
	dstPath := filepath.Join(newBasePath, newPath)

	// Generate base62 for the predetermined hash
	base62, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return nil, fmt.Errorf("failed to generate base62: %w", err)
	}

	imageInfo := &ImageInfo{
		OriginalPath: srcPath,
		NewPath:      dstPath,
		Hash:         hash,
		Base62:       base62,
	}

	// Perform actual file operations if not in dry run mode
	if !config.DryRun {
		status, err := renameOrCopy(srcPath, dstPath, config.Disk)
		if err != nil {
			return imageInfo, fmt.Errorf("file operation failed: %w", err)
		}
		golog.Info("File processed with custom hash", "status", status, "src", srcPath, "dst", dstPath, "hash", hash)
	} else {
		golog.Info("DRY RUN: Would process file with custom hash", "src", srcPath, "dst", dstPath, "hash", hash)
	}

	return imageInfo, nil
}

// processImageFileWithPreCalculatedPath processes a single image file with pre-calculated paths and hash
func processImageFileWithPreCalculatedPath(relativePath, newPath string, hash int32, config Config) (*ImageInfo, error) {
	// Build full source path
	var basePath string
	if config.Disk == "ca6" {
		basePath = OLD_CA6_PATH
	} else {
		basePath = OLD_CA7_PATH
	}
	srcPath := filepath.Join(basePath, relativePath)

	// Build full destination path
	var newBasePath string
	if config.Disk == "ca6" {
		newBasePath = NEW_CA6_PATH
	} else {
		newBasePath = NEW_CA7_PATH
	}
	dstPath := filepath.Join(newBasePath, newPath)

	// Generate base62 for the predetermined hash
	base62, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return nil, fmt.Errorf("failed to generate base62: %w", err)
	}

	imageInfo := &ImageInfo{
		OriginalPath: srcPath,
		NewPath:      dstPath,
		Hash:         hash,
		Base62:       base62,
	}

	// Perform actual file operations if not in dry run mode
	if !config.DryRun {
		status, err := renameOrCopy(srcPath, dstPath, config.Disk)
		if err != nil {
			return imageInfo, fmt.Errorf("file operation failed: %w", err)
		}
		golog.Info("File processed with pre-calculated path", "status", status, "src", srcPath, "dst", dstPath, "hash", hash)
	} else {
		golog.Info("DRY RUN: Would process file with pre-calculated path", "src", srcPath, "dst", dstPath, "hash", hash)
	}

	return imageInfo, nil
}

// loadAndResizeLocalImage loads a local image file and resizes it to fit within the given dimensions
func loadAndResizeLocalImage(imagePath string, maxWidth, maxHeight int) (image.Image, error) {
	// Open the image file
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	// Decode the image
	img, _, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Get original dimensions
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// Calculate new dimensions maintaining aspect ratio
	var newWidth, newHeight int
	if originalWidth > originalHeight {
		// Landscape orientation
		newWidth = maxWidth
		newHeight = int(float64(originalHeight) * float64(maxWidth) / float64(originalWidth))
		if newHeight > maxHeight {
			newHeight = maxHeight
			newWidth = int(float64(originalWidth) * float64(maxHeight) / float64(originalHeight))
		}
	} else {
		// Portrait or square orientation
		newHeight = maxHeight
		newWidth = int(float64(originalWidth) * float64(maxHeight) / float64(originalHeight))
		if newWidth > maxWidth {
			newWidth = maxWidth
			newHeight = int(float64(originalHeight) * float64(maxWidth) / float64(originalWidth))
		}
	}

	// Ensure dimensions are at least 1
	if newWidth < 1 {
		newWidth = 1
	}
	if newHeight < 1 {
		newHeight = 1
	}

	// If no resizing needed, return original image
	if newWidth == originalWidth && newHeight == originalHeight {
		return img, nil
	}

	// Create resized image
	resizedImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))
	draw.CatmullRom.Scale(resizedImg, resizedImg.Bounds(), img, img.Bounds(), draw.Over, nil)

	return resizedImg, nil
}
