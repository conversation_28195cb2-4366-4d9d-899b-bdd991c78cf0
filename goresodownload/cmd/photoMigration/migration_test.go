package main

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

func TestNeedToProcess(t *testing.T) {
	tests := []struct {
		name     string
		prop     bson.M
		expected bool
	}{
		{
			name: "TRB with pho > 0, no phoLH",
			prop: bson.M{
				"src": "TRB",
				"pho": int32(5),
			},
			expected: true,
		},
		{
			name: "TRB with pho > 0, has phoLH",
			prop: bson.M{
				"src":   "TRB",
				"pho":   int32(5),
				"phoLH": []int32{123, 456},
			},
			expected: false,
		},
		{
			name: "DDF with pho > 0, no phoLH",
			prop: bson.M{
				"src": "DDF",
				"pho": 3,
			},
			expected: true,
		},
		{
			name: "OTW with phoIDs, no phoLH",
			prop: bson.M{
				"src":    "OTW",
				"phoIDs": []interface{}{"0", "1"},
			},
			expected: true,
		},
		{
			name: "CLG with phoIDs, no phoLH",
			prop: bson.M{
				"src":    "CLG",
				"phoIDs": []interface{}{"0"},
			},
			expected: true,
		},
		{
			name: "TRB with pho = 0",
			prop: bson.M{
				"src": "TRB",
				"pho": int32(0),
			},
			expected: false,
		},
		{
			name: "OTW without phoIDs",
			prop: bson.M{
				"src": "OTW",
			},
			expected: false,
		},
		{
			name: "Unknown source type",
			prop: bson.M{
				"src": "UNKNOWN",
				"pho": int32(5),
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := needToProcess(tt.prop)
			if result != tt.expected {
				t.Errorf("needToProcess() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestBuildDDFPath(t *testing.T) {
	tests := []struct {
		name     string
		params   PathBuildParams
		expected string
		hasError bool
	}{
		{
			name: "Valid DDF path",
			params: PathBuildParams{
				Prop: bson.M{
					"_id": "DDF23214174",
				},
				ImageNum: 1,
			},
			expected: "crea/ddf/img/174/23214174_1.jpg",
			hasError: false,
		},
		{
			name: "Valid DDF path with different image number",
			params: PathBuildParams{
				Prop: bson.M{
					"_id": "DDF26226669",
				},
				ImageNum: 5,
			},
			expected: "crea/ddf/img/669/26226669_5.jpg",
			hasError: false,
		},
		{
			name: "Invalid DDF ID - too short",
			params: PathBuildParams{
				Prop: bson.M{
					"_id": "DDF12",
				},
				ImageNum: 1,
			},
			expected: "",
			hasError: true,
		},
		{
			name: "Invalid DDF ID - wrong prefix",
			params: PathBuildParams{
				Prop: bson.M{
					"_id": "TRB23214174",
				},
				ImageNum: 1,
			},
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := buildDDFPath(tt.params)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("buildDDFPath() expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("buildDDFPath() unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("buildDDFPath() = %v, expected %v", result, tt.expected)
				}
			}
		})
	}
}

func TestBuildTRBPath(t *testing.T) {
	tests := []struct {
		name     string
		params   PathBuildParams
		expected string
		hasError bool
	}{
		{
			name: "TRB path - first image",
			params: PathBuildParams{
				Prop: bson.M{
					"sid": "1234567",
				},
				ImageNum: 1,
			},
			expected: "treb/mls/1/567/1234567.jpg",
			hasError: false,
		},
		{
			name: "TRB path - second image",
			params: PathBuildParams{
				Prop: bson.M{
					"sid": "1234567",
				},
				ImageNum: 2,
			},
			expected: "treb/mls/2/567/1234567_2.jpg",
			hasError: false,
		},
		{
			name: "Invalid sid - too short",
			params: PathBuildParams{
				Prop: bson.M{
					"sid": "12",
				},
				ImageNum: 1,
			},
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := buildTRBPath(tt.params)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("buildTRBPath() expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("buildTRBPath() unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("buildTRBPath() = %v, expected %v", result, tt.expected)
				}
			}
		})
	}
}

func TestBuildOTWCLGPath(t *testing.T) {
	tests := []struct {
		name     string
		params   PathBuildParams
		expected string
		hasError bool
	}{
		{
			name: "OTW path",
			params: PathBuildParams{
				Prop: bson.M{
					"_id":   "OTW731271",
					"orgId": 8098061,
				},
				Src:     "OTW",
				ImageID: "0",
			},
			expected: "oreb/mls/80/61/731271_0.jpg",
			hasError: false,
		},
		{
			name: "CLG path",
			params: PathBuildParams{
				Prop: bson.M{
					"_id":   "CLGC4148952",
					"orgId": 1382288,
				},
				Src:     "CLG",
				ImageID: "1",
			},
			expected: "oreb/mls/22/88/C4148952_1.jpg",
			hasError: false,
		},
		{
			name: "Invalid orgId - too short",
			params: PathBuildParams{
				Prop: bson.M{
					"_id":   "OTW731271",
					"orgId": 123,
				},
				Src:     "OTW",
				ImageID: "0",
			},
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := buildOTWCLGPath(tt.params)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("buildOTWCLGPath() expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("buildOTWCLGPath() unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("buildOTWCLGPath() = %v, expected %v", result, tt.expected)
				}
			}
		})
	}
}

func TestGetTimeField(t *testing.T) {
	now := time.Now()
	
	tests := []struct {
		name      string
		prop      bson.M
		fieldName string
		expected  time.Time
	}{
		{
			name: "Valid time field",
			prop: bson.M{
				"ts": now,
			},
			fieldName: "ts",
			expected:  now,
		},
		{
			name: "Missing time field",
			prop: bson.M{
				"other": "value",
			},
			fieldName: "ts",
			expected:  time.Time{},
		},
		{
			name: "Invalid time field type",
			prop: bson.M{
				"ts": "not a time",
			},
			fieldName: "ts",
			expected:  time.Time{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getTimeField(tt.prop, tt.fieldName)
			if !result.Equal(tt.expected) {
				t.Errorf("getTimeField() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
