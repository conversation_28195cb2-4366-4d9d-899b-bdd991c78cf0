<!-- Generated by SpecStory -->

# Review goupload package documentation and code (2025-07-29 14:06Z)

_**User**_

作为编程专家，帮我review goupload package docs/refine-readme-api branch 代码，除了ut外，有没有其他的风险，问题，bug

---

_**Assistant**_

我来帮您review goupload package的代码。首先让我查看项目结构和相关文件。


---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📁 `goupload` |
| 📄 `ca6_ca7_分离下载方案.md` |
| 📁 `goresodownload` |
| 📁 `gofile` |
| 📁 `golevelstore` |
| 📁 `goauth` |
| 📁 `goapidocs` |
| 📁 `gomongo` |
| 📁 `gospeedmeter` |
| 📁 `gohelper` |
| 📁 `goconfig` |
| 📁 `gomail` |
| 📁 `goprocess` |
| 📁 `gowatch` |
| 📁 `gostreaming` |
| 📁 `folder_sync` |
| 📁 `scripts` |
| 📁 `goapp` |
| 📁 `gobase` |
| 📁 `.cursor` |
| 📁 `gosms` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |

</details>

---

<details>
            <summary>Listed directory goupload • **22** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📄 `local.test.ini` |
| 📄 `upload_directory.go` |
| 📄 `upload_directory_test.go` |
| 📄 `upload_helper.go` |
| 📄 `upload_helper_test.go` |
| 📄 `upload_chunked_test.go` |
| 📄 `upload_core.go` |
| 📄 `upload_core_test.go` |
| 📁 `testserver` |
| 📄 `types.go` |
| 📄 `upload_chunked.go` |
| 📄 `delete_core.go` |
| 📄 `delete_core_test.go` |
| 📄 `garage.toml` |
| 📄 `go.mod` |
| 📄 `go.sum` |
| 📄 `test_setup.go` |
| 📄 `api.md` |
| 📁 `.github` |
| 📄 `.gitignore` |
| 📄 `README.md` |

</details>

---

Read file: goupload/README.md

---

现在让我查看核心代码文件，了解实现细节：


---

Read file: goupload/upload_core.go

---

Read file: goupload/upload_directory.go

---

Read file: goupload/upload_chunked.go

---

Read file: goupload/delete_core.go

---

Read file: goupload/types.go

---

Read file: goupload/api.md

---

现在让我查看一些关键的测试文件，了解潜在的问题：


---

<details>
            <summary>Grep search for "TODO|FIXME|BUG|HACK" • **15** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/level_store_helper.go` | L69 | `golog.Debug("Registered DirKeyStore for stats-based L1", "board", board)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/level_store_helper.go` | L75 | `golog.Debug("Unregistered DirKeyStore", "board", board)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/level_store_helper.go` | L364 | `golog.Debug("generated file path components",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L303 | `golog.Debug("Using custom directories for stats", "board", store.prefix, "dirs", dirs)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L347 | `golog.Debug("Saving stats to directories", "dirs", dirs, "l1_count", len(l1StatsMap))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L683 | `golog.Debug("fetched dir array", "l1", l1, "dirArray", len(dirArray))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L729 | `golog.Debug("Added new L2 directory array to tmpDirMap",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L829 | `golog.Debug("Got L2 directory list",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L880 | `golog.Debug("Using existing L2 directory list",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store.go` | L987 | `golog.Debug("Saved current L1", "board", store.prefix, "l1", store.curL1)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/golevelstore/dir_key_store_test.go` | L2467 | `// 4. Verify L1 was upgraded (this should fail with current buggy logic)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/sync_fixes_test.go` | L29 | `func (m *MockLogger) DebugP(peerID string, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/sync_fixes_test.go` | L30 | `m.t.Logf("[DEBUG:%s] %s", peerID, fmt.Sprintf(format, args...))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/sync_fixes_test.go` | L44 | `func (m *MockLogger) Debug(format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/sync_fixes_test.go` | L45 | `m.t.Logf("[DEBUG] %s", fmt.Sprintf(format, args...))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/sync_fixes_test.go` | L88 | `syncLogger, err := logger.NewLogger(logsPath, false) // false = not debug mode` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/dev/diagnose/test_handshake.go` | L21 | `func (l *TestLogger) Debug(format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/dev/diagnose/test_handshake.go` | L22 | `log.Printf("[DEBUG] "+format, args...)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/dev/diagnose/test_handshake.go` | L33 | `func (l *TestLogger) DebugP(peerID string, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/dev/diagnose/test_handshake.go` | L34 | `log.Printf("[DEBUG][%s] "+format, append([]interface{}{peerID}, args...)...)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_sync_test.go` | L185 | `debug: true`,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_sync_test.go` | L223 | `debug: true`,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_sync_test.go` | L438 | `// Capture output for debugging` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_external_folder_copy_test.go` | L66 | `// Check server logs for debugging` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_external_folder_copy_test.go` | L139 | `debug: true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_external_folder_copy_test.go` | L205 | `"tasks.txt":         "TODO:\n- Implement feature A\n- Fix bug B\n- Update documentation\n",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_external_folder_copy_test.go` | L275 | `// Debug: Print file lists` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_deadlock_prevention_test.go` | L274 | `// Debug: List what actually exists` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/test/e2e_deadlock_prevention_test.go` | L275 | `t.Logf("=== Debug: Checking directory sync ===")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L30 | `var debugMode bool` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L35 | `flag.BoolVar(&debugMode, "v", false, "Enable verbose (debug) logging")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L52 | `// Override debug setting from CLI flag if provided` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L53 | `if debugMode {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L54 | `cfg.Debug = true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L58 | `setupLogger, err := logger.NewLogger("/tmp/logs", cfg.Debug)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L77 | `appLogger, err := logger.NewLogger(cfg.Folder.LogsPath, cfg.Debug)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L83 | `appLogger.Debug("Configuration loaded: %+v", *cfg)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L258 | `log.Debug("Current memory usage: %s (Sys: %s, GC_CPU_Fraction: %.2f)",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/main.go` | L262 | `log.Debug("Memory monitor stopping.")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L17 | `DEBUG LogLevel = iota // Detailed information, typically only of interest when diagnosing problems.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L27 | `case DEBUG:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L28 | `return "DEBUG"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L48 | `logLevel   LogLevel               // Current logging level (DEBUG, INFO, etc.)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L54 | `// debugMode enables or disables DEBUG level logging.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L55 | `func NewLogger(logsPath string, debugMode bool) (*Logger, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L69 | `if debugMode {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L70 | `logger.logLevel = DEBUG` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L194 | `// Debug logs a message at DEBUG level.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L195 | `func (l *Logger) Debug(format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L196 | `l.output(DEBUG, "", format, args...)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L199 | `// DebugP logs a message at DEBUG level specific to a peer.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L200 | `func (l *Logger) DebugP(peerID string, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger.go` | L201 | `l.output(DEBUG, peerID, format, args...)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L82 | `{logger.DEBUG, "DEBUG"},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L104 | `// Test successful creation with debug mode` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L107 | `t.Fatalf("NewLogger failed with debug mode: %v", err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L113 | `// Test successful creation without debug mode` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L116 | `t.Fatalf("NewLogger failed without debug mode: %v", err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L144 | `// TestLoggingLevels tests that messages are logged according to their level and debug mode.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L151 | `debugMode bool` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L156 | `{"DebugMode: DEBUG, Log: DEBUG", true, (*logger.Logger).Debug, "DEBUG", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L157 | `{"DebugMode: DEBUG, Log: INFO", true, (*logger.Logger).Info, "INFO", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L158 | `{"DebugMode: DEBUG, Log: WARN", true, (*logger.Logger).Warn, "WARN", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L159 | `{"DebugMode: DEBUG, Log: ERROR", true, (*logger.Logger).Error, "ERROR", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L160 | `{"DebugMode: DEBUG, Log: FATAL (will exit)", true, func(l *logger.Logger, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L164 | `{"DebugMode: INFO, Log: DEBUG", false, (*logger.Logger).Debug, "DEBUG", false},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L165 | `{"DebugMode: INFO, Log: INFO", false, (*logger.Logger).Info, "INFO", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L166 | `{"DebugMode: INFO, Log: WARN", false, (*logger.Logger).Warn, "WARN", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L167 | `{"DebugMode: INFO, Log: ERROR", false, (*logger.Logger).Error, "ERROR", true},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L168 | `{"DebugMode: INFO, Log: FATAL (will exit)", false, func(l *logger.Logger, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L178 | `l, err := logger.NewLogger(tempLogsDir, tt.debugMode)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/logger/logger_test.go` | L229 | `l, err := logger.NewLogger(tempLogsDir, true) // Debug mode to log everything` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/queue.go` | L111 | `// Try to extract file path and event type from JSON data for debugging` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/queue.go` | L126 | `qm.logger.DebugP(qm.peerID, "Added queue item with ID %d, priority %d, size %d bytes%s", id, priority, len(data), eventInfo)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/queue.go` | L165 | `qm.logger.DebugP(qm.peerID, "Retrieved next queue item ID %d%s", item.ID, eventInfo)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/queue.go` | L186 | `qm.logger.DebugP(qm.peerID, "Removed queue item ID %d", id)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/queue.go` | L232 | `qm.logger.DebugP(qm.peerID, "Removed queue item ID %d with MessageID %d", id, messageID)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/meta_test.go` | L21 | `Debugs []string` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/meta_test.go` | L44 | `func (m *MockLogger) DebugP(peerID string, format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/meta_test.go` | L47 | `m.Debugs = append(m.Debugs, fmt.Sprintf("PEER %s: %s", peerID, fmt.Sprintf(format, args...)))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/meta_test.go` | L48 | `m.t.Logf("[MockLogger DEBUGP] PEER %s: %s", peerID, fmt.Sprintf(format, args...))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/folder_sync/pkg/meta/meta_test.go` | L77 | `func (m *MockLogger) Debug(format string, args ...interface{}) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/cmd/goresodownload/main.go` | L102 | `PROCESS_STATUS_ID = "watchPhotoDownload" + gBoardType` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L191 | `golog.Debug("Starting main program from:", mainDir)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L214 | `golog.Debug("Main program started with PID:", cmd.Process.Pid)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L264 | `golog.Debug("path", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L484 | `golog.Debug("Found raw document:", rawOne["_id"])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L491 | `golog.Debug("Deleted existing document")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L535 | `golog.Debug("Inserting document with ID:", rawOne["_id"])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L539 | `golog.Debug("Document inserted successfully")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L558 | `golog.Debug("Retrieved merged document",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L573 | `golog.Debug("Found phoLH array",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L652 | `golog.Debug("Retrieved merged document",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L667 | `golog.Debug("Found phoLH array",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L852 | `golog.Debug("Waiting for failed task to be recorded",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goresodownload/test/e2e/main_e2e_test.go` | L976 | `golog.Debug("Waiting for failed task to be recorded",` |

</details>

---

<details>
            <summary>Grep search for "TODO|FIXME|BUG|HACK" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "panic|fatal|error" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L4 | `"errors"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L17 | `func GenerateFilename(originalFilename string) (string, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L26 | `return "", fmt.Errorf("failed to generate unique id: %w", err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L37 | `func ValidateFilename(filename string) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L39 | `return fmt.Errorf("filename cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L44 | `return fmt.Errorf("filename length cannot exceed 255 characters")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L51 | `return fmt.Errorf("filename cannot contain illegal character: %s", char)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L67 | `return fmt.Errorf("filename cannot be a reserved name: %s", reserved)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L75 | `func ExecuteWithRetry(operation func() error, maxRetries int, delay time.Duration) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L76 | `var lastErr error` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L101 | `func parseSize(sizeStr string) (int64, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L104 | `return 0, errors.New("empty size string")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L127 | `return 0, fmt.Errorf("invalid number for size: %q", numPart)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L149 | `return 0, fmt.Errorf("invalid unit for size: %q", unitPart)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L154 | `return 0, fmt.Errorf("size cannot be negative: %f", value)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper.go` | L159 | `return 0, fmt.Errorf("size value out of range: %s%s", numPart, unitPart)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L4 | `"errors"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L17 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L24 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L30 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L35 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L55 | `assert.NoError(t, err, "生成文件名应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L82 | `assert.NoError(t, err, "文件名应该验证成功: %s", filename)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L89 | `assert.Error(t, err, "空文件名应该验证失败")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L90 | `assert.Contains(t, err.Error(), "cannot be empty", "错误信息应该包含空文件名信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L97 | `assert.Error(t, err, "过长文件名应该验证失败")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L98 | `assert.Contains(t, err.Error(), "cannot exceed 255 characters", "错误信息应该包含长度限制")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L108 | `assert.Error(t, err, "包含非法字符的文件名应该验证失败: %s", filename)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L109 | `assert.Contains(t, err.Error(), "illegal character", "错误信息应该包含非法字符信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L110 | `assert.Contains(t, err.Error(), char, "错误信息应该包含具体的非法字符")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L125 | `assert.Error(t, err, "保留名称应该验证失败: %s", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L126 | `assert.Contains(t, err.Error(), "reserved name", "错误信息应该包含保留名称信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L130 | `assert.Error(t, err, "小写保留名称应该验证失败: %s", strings.ToLower(name))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L134 | `assert.Error(t, err, "带扩展名的保留名称应该验证失败: %s.txt", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L146 | `assert.Error(t, err, "混合大小写保留名称应该验证失败: %s", mixedCase)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L164 | `assert.NoError(t, err, "非保留名称应该验证成功: %s", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L171 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L177 | `assert.NoError(t, err, "成功操作应该立即返回")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L184 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L187 | `return errors.New("temporary failure")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L196 | `assert.NoError(t, err, "重试后成功的操作应该返回nil")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L204 | `expectedError := errors.New("persistent failure")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L206 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L208 | `return expectedError` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L215 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L216 | `assert.Equal(t, expectedError, err, "应该返回最后一次的错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L224 | `expectedError := errors.New("failure")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L226 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L228 | `return expectedError` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L232 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L233 | `assert.Equal(t, expectedError, err, "应该返回操作的错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L240 | `expectedError := errors.New("failure")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L242 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L244 | `return expectedError` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L249 | `assert.NoError(t, err, "负数重试次数应该返回nil（没有执行操作）")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L281 | `assert.NoError(t, err, "时间戳应该能被解析为整数")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L345 | `assert.NoError(t, err, "解析应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L351 | `func TestParseSize_Errors(t *testing.T) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L352 | `errorCases := []struct {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L354 | `expectError string` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L369 | `for _, tc := range errorCases {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L371 | `assert.Error(t, err, "应该返回错误: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L373 | `assert.Contains(t, err.Error(), tc.expectError,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L374 | `"错误信息应该包含预期内容: %s -> %s", tc.input, err.Error())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L395 | `assert.NoError(t, err, "解析应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L405 | `assert.Contains(t, err.Error(), "out of range", "超大数值应该触发溢出检查")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L424 | `assert.Contains(t, err.Error(), "out of range", "大数值溢出应该有相应错误信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L461 | `assert.NoError(t, err, "生成长文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L467 | `assert.NoError(t, err, "生成包含特殊字符的文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L488 | `operation := func() error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L491 | `return errors.New("fail")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L497 | `assert.NoError(t, err, "零延迟重试应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L39 | `FailedFiles     []DirectoryFileError // 失败的文件` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L44 | `// DirectoryFileError 文件上传错误` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L45 | `type DirectoryFileError struct {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L47 | `Error        string // 错误信息` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L64 | `) (*DirectoryUploadResult, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L140 | `func validateDirectoryRequest(request *DirectoryUploadRequest) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L142 | `return fmt.Errorf("request cannot be nil")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L145 | `return fmt.Errorf("site cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L148 | `return fmt.Errorf("entryName cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L151 | `return fmt.Errorf("userID cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L154 | `return fmt.Errorf("directoryName cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L157 | `return fmt.Errorf("files list cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L163 | `return fmt.Errorf("file %d: relativePath cannot be empty", i)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L166 | `return fmt.Errorf("file %d: reader cannot be nil", i)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L170 | `return fmt.Errorf("file %d: %w", i, err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L178 | `func validateRelativePath(path string) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L181 | `return fmt.Errorf("path cannot be empty")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L186 | `return fmt.Errorf("path traversal not allowed: %s", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L194 | `return fmt.Errorf("path traversal not allowed: %s", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L199 | `return fmt.Errorf("absolute path not allowed: %s", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L204 | `return fmt.Errorf("absolute path not allowed: %s", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L209 | `return fmt.Errorf("path too long (max 500 chars): %s", path)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L214 | `return fmt.Errorf("path traversal not allowed: %s", path)` |

</details>

---

<details>
            <summary>Grep search for "sync\.|mutex|lock|race" • **7** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L34 | `chunkManager.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L36 | `chunkManager.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L671 | `var wg sync.WaitGroup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L953 | `var wg sync.WaitGroup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L46 | `mu          sync.Mutex // 保护此状态对象的并发访问` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L54 | `mu      sync.RWMutex` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L55 | `once    sync.Once // 用于确保清理任务只启动一次` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L70 | `m.mu.RLock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L71 | `defer m.mu.RUnlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L78 | `m.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L79 | `defer m.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L85 | `m.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L86 | `defer m.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L92 | `m.mu.RLock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L100 | `m.mu.RUnlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L116 | `// 使用 sync.Once 确保该任务在整个应用程序生命周期中只启动一次。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L313 | `state.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L314 | `defer state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L355 | `// 注意：我们直接调用 unlocked 版本的清理函数，因为它已经持有锁。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L356 | `if cleanupErr := abortAndCleanupUnlocked(state); cleanupErr != nil {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L384 | `// Lock the state to prevent race conditions during cleanup.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L385 | `state.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L386 | `defer state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L388 | `return abortAndCleanupUnlocked(state)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L391 | `// abortAndCleanupUnlocked 是实际执行清理工作的内部函数。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L393 | `func abortAndCleanupUnlocked(state *ChunkedUploadState) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L429 | `// 2. Lock and claim ownership to prevent other goroutines from operating.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L430 | `state.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L432 | `state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L436 | `state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L442 | `state.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L444 | `state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L466 | `state.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L468 | `state.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L504 | `s.mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L505 | `defer s.mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L26 | `var s3ClientCache sync.Map` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L27 | `var s3CacheCleanupOnce sync.Once` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L121 | `golog.Info("race condition averted: another goroutine created the s3 client first", "key", cacheKey)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L562 | `var wg sync.WaitGroup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L563 | `var mu sync.Mutex // To safely append to rollbackErrors from multiple goroutines` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L584 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L586 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1532 | `largeContent := strings.Repeat("Large file content block. ", 1000) // 约27KB` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L69 | `var wg sync.WaitGroup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L70 | `var mu sync.Mutex // 保护结果并发更新` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L83 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L88 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L90 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L96 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L102 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L109 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L116 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L124 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L126 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L133 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/test_setup.go` | L11 | `testConfigSetupOnce sync.Once` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L95 | `var mu sync.Mutex` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L96 | `var wg sync.WaitGroup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L108 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L113 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L115 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L121 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L127 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L134 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L141 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L149 | `mu.Unlock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L151 | `mu.Lock()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core_test.go` | L158 | `mu.Unlock()` |

</details>

---

<details>
            <summary>Grep search for "memory|leak|goroutine|context" • **6** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L61 | `ctx context.Context,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory.go` | L298 | `ctx context.Context,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L62 | `result, err := UploadDirectory(context.Background(), statsUpdater, request)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L84 | `ctx := context.Background()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L218 | `_, err = UploadDirectory(context.Background(), statsUpdater, request)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L396 | `result, err := UploadDirectory(context.Background(), statsUpdater, request)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L479 | `s3ProviderMap, err := getS3ProviderMap(context.Background(), config.Storage)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_directory_test.go` | L484 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L106 | `if err := AbortChunkedUpload(context.Background(), id); err != nil {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L115 | `// startCleanupTask 启动一个后台 goroutine，定期清理过期的上传任务。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L133 | `func InitiateChunkedUpload(ctx context.Context, provider PathAndConfigProvider, site, entryName, uid, originalFilename string, totalFileSize int64) (string, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L296 | `func UploadChunk(ctx context.Context, uploadID string, chunkNumber int, reader io.Reader) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L377 | `func AbortChunkedUpload(ctx context.Context, uploadID string) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L394 | `// 1. 检查是否已被其他goroutine处理` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L422 | `func CompleteChunkedUpload(ctx context.Context, statsUpdater StatsUpdater, uploadID string, expectedChunks int, s3ProviderMap map[string]levelStore.S3ProviderConfig) (*UploadResult, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L429 | `// 2. Lock and claim ownership to prevent other goroutines from operating.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked.go` | L454 | `defer func() { _ = fileReader.Close() }() // Ensure the file reader is always closed to prevent resource leaks.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L87 | `PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L88 | `DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L92 | `func getOrNewS3Client(ctx context.Context, provider levelStore.S3ProviderConfig) (s3API, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L117 | `// 如果在创建期间，另一个goroutine已经存入了客户端，actual 将是那个已存在的客户端。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L121 | `golog.Info("race condition averted: another goroutine created the s3 client first", "key", cacheKey)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L150 | `func Upload(ctx context.Context, statsUpdater StatsUpdater, site, entryName, uid string, reader io.Reader, originalFilename string, clientDeclaredSize int64) (*UploadResult, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L311 | `func WriteToLocationsWithRollback(ctx context.Context, config *levelStore.UserUploadConfig, s3ProviderMap map[string]levelStore.S3ProviderConfig, reader io.ReadSeeker, relativePath string, opts WriteOptions, metadata map[string]string, fileInfo *` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L432 | `func getS3ProviderMap(_ context.Context, storage []levelStore.StorageConfig) (map[string]levelStore.S3ProviderConfig, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L512 | `func writeFileToS3(ctx context.Context, s3Client s3API, reader io.ReadSeeker, bucket, key string, opts WriteOptions, metadata map[string]string) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L520 | `timeoutCtx, cancel := context.WithTimeout(ctx, opts.S3Timeout)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L560 | `func rollbackWrittenFiles(ctx context.Context, writtenPaths []WrittenPath, _ map[string]levelStore.S3ProviderConfig, s3ClientMap map[string]s3API, opts WriteOptions) []error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L563 | `var mu sync.Mutex // To safely append to rollbackErrors from multiple goroutines` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L595 | `func deleteS3Object(ctx context.Context, s3Client s3API, bucket, key string, opts WriteOptions) error {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core.go` | L597 | `timeoutCtx, cancel := context.WithTimeout(ctx, opts.S3Timeout)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/delete_core.go` | L40 | `func Delete(ctx context.Context, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L4 | `"context"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L27 | `func (m *mockS3API) PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L35 | `func (m *mockS3API) DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L392 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L443 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L472 | `ctx := context.Background()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L539 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L624 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L629 | `err := writeFileToS3(context.Background(), mockS3, reader, bucket, key, opts, metadata)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L653 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L658 | `err := writeFileToS3(context.Background(), mockS3, reader, bucket, key, opts, metadata)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L679 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L684 | `err := deleteS3Object(context.Background(), mockS3, bucket, key, opts)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L733 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L739 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L808 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L817 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L880 | `mock.AnythingOfType("*context.timerCtx"),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L886 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L942 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1027 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1076 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1112 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1144 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1174 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1203 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1232 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1265 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1286 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1317 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1354 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1374 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1431 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1463 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1480 | `func TestUpload_ContextCancellation(t *testing.T) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1488 | `content := "Test content for context cancellation"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1492 | `ctx, cancel := context.WithCancel(context.Background())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1536 | `context.Background(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1721 | `result, err := getS3ProviderMap(context.Background(), storage)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1739 | `result, err := getS3ProviderMap(context.Background(), storage)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1761 | `result, err := getS3ProviderMap(context.Background(), storage)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1883 | `rollbackErrors := rollbackWrittenFiles(context.Background(), writtenPaths, nil, nil, writeOpts)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1916 | `rollbackErrors := rollbackWrittenFiles(context.Background(), writtenPaths, nil, nil, writeOpts)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_core_test.go` | L1930 | `rollbackErrors1 := rollbackWrittenFiles(context.Background(), []WrittenPath{}, nil, nil, writeOpts)` |

</details>

---

Read file: goupload/upload_core.go

---

Read file: goupload/upload_core.go

---

Read file: goupload/upload_chunked.go

---

现在让我查看一些测试文件，了解潜在的问题：


---

<details>
            <summary>Grep search for "t\.Fatal|t\.Error|assert\.|require\." • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L17 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L18 | `assert.NotEmpty(t, filename, "文件名不应该为空")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L19 | `assert.True(t, strings.HasSuffix(filename, ".txt"), "应该保留原始扩展名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L20 | `assert.True(t, len(filename) > 4, "生成的文件名应该包含ID和扩展名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L24 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L25 | `assert.NotEmpty(t, filename2, "文件名不应该为空")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L26 | `assert.False(t, strings.Contains(filename2, "."), "不应该有扩展名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L30 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L31 | `assert.NotEmpty(t, filename3, "文件名不应该为空")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L35 | `assert.NoError(t, err, "生成文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L36 | `assert.NotEqual(t, filename, filename4, "多次调用应该产生不同的文件名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L55 | `assert.NoError(t, err, "生成文件名应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L56 | `assert.True(t, strings.HasSuffix(filename, tc.expected),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L82 | `assert.NoError(t, err, "文件名应该验证成功: %s", filename)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L89 | `assert.Error(t, err, "空文件名应该验证失败")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L90 | `assert.Contains(t, err.Error(), "cannot be empty", "错误信息应该包含空文件名信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L97 | `assert.Error(t, err, "过长文件名应该验证失败")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L98 | `assert.Contains(t, err.Error(), "cannot exceed 255 characters", "错误信息应该包含长度限制")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L108 | `assert.Error(t, err, "包含非法字符的文件名应该验证失败: %s", filename)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L109 | `assert.Contains(t, err.Error(), "illegal character", "错误信息应该包含非法字符信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L110 | `assert.Contains(t, err.Error(), char, "错误信息应该包含具体的非法字符")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L125 | `assert.Error(t, err, "保留名称应该验证失败: %s", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L126 | `assert.Contains(t, err.Error(), "reserved name", "错误信息应该包含保留名称信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L130 | `assert.Error(t, err, "小写保留名称应该验证失败: %s", strings.ToLower(name))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L134 | `assert.Error(t, err, "带扩展名的保留名称应该验证失败: %s.txt", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L146 | `assert.Error(t, err, "混合大小写保留名称应该验证失败: %s", mixedCase)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L164 | `assert.NoError(t, err, "非保留名称应该验证成功: %s", name)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L177 | `assert.NoError(t, err, "成功操作应该立即返回")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L178 | `assert.Equal(t, 1, callCount, "成功操作只应该被调用一次")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L196 | `assert.NoError(t, err, "重试后成功的操作应该返回nil")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L197 | `assert.Equal(t, 3, callCount, "操作应该被调用3次")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L198 | `assert.True(t, elapsed >= 10*time.Millisecond, "应该有重试延迟")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L215 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L216 | `assert.Equal(t, expectedError, err, "应该返回最后一次的错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L217 | `assert.Equal(t, 4, callCount, "操作应该被调用4次（初始+3次重试）")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L218 | `assert.True(t, elapsed >= 15*time.Millisecond, "应该有3次重试延迟")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L232 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L233 | `assert.Equal(t, expectedError, err, "应该返回操作的错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L234 | `assert.Equal(t, 1, callCount, "操作应该被调用1次")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L249 | `assert.NoError(t, err, "负数重试次数应该返回nil（没有执行操作）")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L250 | `assert.Equal(t, 0, callCount, "操作不应该被调用")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L270 | `assert.Equal(t, tc.expected, result, "路径种子应该正确生成")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L274 | `assert.Len(t, parts, 3, "路径种子应该包含3个部分")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L275 | `assert.Equal(t, tc.filename, parts[0], "第一部分应该是文件名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L276 | `assert.Equal(t, tc.uid, parts[2], "第三部分应该是用户ID")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L281 | `assert.NoError(t, err, "时间戳应该能被解析为整数")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L282 | `assert.Equal(t, tc.timestamp, parsedTimestamp, "时间戳应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L345 | `assert.NoError(t, err, "解析应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L346 | `assert.Equal(t, tc.expected, result, "解析结果应该正确: %s -> %d", tc.input, tc.expected)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L371 | `assert.Error(t, err, "应该返回错误: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L372 | `assert.Equal(t, int64(0), result, "错误时应该返回0")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L373 | `assert.Contains(t, err.Error(), tc.expectError,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L395 | `assert.NoError(t, err, "解析应该成功: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L396 | `assert.Equal(t, tc.expected, result, "解析结果应该正确: %s -> %d", tc.input, tc.expected)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L405 | `assert.Contains(t, err.Error(), "out of range", "超大数值应该触发溢出检查")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L424 | `assert.Contains(t, err.Error(), "out of range", "大数值溢出应该有相应错误信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L426 | `assert.Equal(t, tc.expected, result, "大数值解析结果应该正确: %s", tc.input)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L449 | `assert.Equal(t, tc.expected, result,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L461 | `assert.NoError(t, err, "生成长文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L462 | `assert.True(t, strings.HasSuffix(filename, ".txt"), "应该保留扩展名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L467 | `assert.NoError(t, err, "生成包含特殊字符的文件名应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L468 | `assert.True(t, strings.HasSuffix(filename, ".js"), "应该保留最后的扩展名")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L477 | `assert.Equal(t, expectedParts, actualParts, "特殊字符应该被保留")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L481 | `assert.Contains(t, result, "_-1_", "负时间戳应该被正确处理")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L497 | `assert.NoError(t, err, "零延迟重试应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_helper_test.go` | L498 | `assert.Equal(t, 2, callCount, "应该调用2次")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L24 | `assert.NoError(t, err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L64 | `assert.True(t, found, "应该能找到添加的状态")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L65 | `assert.Equal(t, state.UploadID, retrievedState.UploadID, "状态数据应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L69 | `assert.False(t, found, "不存在的ID应该返回false")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L74 | `assert.False(t, found, "删除后应该找不到状态")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L113 | `assert.NoError(t, err, "初始化应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L114 | `assert.NotEmpty(t, uploadID, "应该返回上传ID")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L118 | `assert.True(t, found, "状态应该被创建")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L119 | `assert.Equal(t, "TEST", state.Site, "站点应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L120 | `assert.Equal(t, "chunked_upload", state.EntryName, "条目名应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L121 | `assert.Equal(t, "user123", state.UID, "用户ID应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L122 | `assert.Equal(t, "large_file.zip", state.OriginalFilename, "文件名应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L123 | `assert.Equal(t, int64(1024*1024*100), state.TotalFileSize, "文件大小应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L126 | `assert.DirExists(t, state.TempChunkDir, "临时目录应该被创建")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L159 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L160 | `assert.Empty(t, uploadID, "不应该返回上传ID")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L161 | `assert.Contains(t, err.Error(), "exceeds limit", "错误信息应该包含超限信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L193 | `assert.Error(t, err, "应该返回错误")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L194 | `assert.Empty(t, uploadID, "不应该返回上传ID")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L195 | `assert.Contains(t, err.Error(), "must be greater than zero", "错误信息应该包含零大小信息")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L216 | `assert.NoError(t, err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L225 | `assert.NoError(t, err, "分块上传应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L229 | `assert.True(t, found, "状态应该存在")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L230 | `assert.True(t, state.UploadedChunks[1], "分块1应该被标记为已上传")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L231 | `assert.Equal(t, int64(len(chunkContent)), state.TotalBytesReceived, "已接收字节数应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L235 | `assert.FileExists(t, chunkPath, "分块文件应该存在")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L239 | `assert.NoError(t, err, "应该能读取分块文件")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L240 | `assert.Equal(t, chunkContent, string(content), "分块内容应该正确")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L257 | `assert.NoError(t, err)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L263 | `assert.NoError(t, err, "第一次上传应该成功")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L267 | `assert.NoError(t, err, "第二次上传应该成功（幂等性）")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L271 | `assert.True(t, found)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L272 | `assert.True(t, state.UploadedChunks[1], "分块1应该被标记为已上传")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/github/go/goupload/upload_chunked_test.go` | L273 | `assert.Equal(t, int64(len(chunkContent)), state.TotalBytesReceived, "字节数不应该重复计算")` |

</details>

---

现在让我查看一些关键的配置和依赖：


---

Read file: goupload/go.mod

---

基于我对goupload package的全面review，我发现了以下风险、问题和潜在的bug：

## 🔍 GoUpload Package Review 结果

### ✅ 优点
1. **代码结构良好** - 模块化设计，职责分离清晰
2. **测试覆盖全面** - 包含单元测试和集成测试
3. **并发安全** - 正确使用了sync.Mutex和sync.RWMutex
4. **错误处理完善** - 提供了详细的错误信息和回滚机制
5. **文档详细** - README和API文档都很完整

### ⚠️ 主要风险和问题

#### 1. **内存泄漏风险**
```go
// upload_core.go:662-800
func getFileInfoWithRewindableReader(reader io.Reader, filename string, maxAllowedSize int64, tempDir string) (io.ReadSeeker, *FileInfo, func(), error) {
    // 对于non-seeker reader，会创建临时文件
    tempFile, err := os.CreateTemp(tempDir, "upload-*.tmp")
    // ...
    cleanup := func() {
        _ = tempFile.Close()
        if err := os.Remove(tempFile.Name()); err != nil && !os.IsNotExist(err) {
            golog.Warn("Failed to remove temporary file for non-seeker", "path", tempFile.Name(), "error", err)
        }
    }
}
```
**风险**: 如果调用者忘记调用cleanup函数，临时文件会泄漏。

#### 2. **分块上传状态管理风险**
```go
// upload_chunked.go:52-56
type chunkedUploadStateManager struct {
    uploads map[string]*ChunkedUploadState
    mu      sync.RWMutex
    once    sync.Once
}
```
**风险**: 
- 内存中的状态管理，服务重启会丢失所有未完成的上传
- 没有持久化机制，生产环境风险高
- 24小时超时清理可能不够及时

#### 3. **S3客户端缓存潜在问题**
```go
// upload_core.go:25-26
var s3ClientCache sync.Map
var s3CacheCleanupOnce sync.Once
```
**风险**:
- 缓存清理间隔5分钟可能不够及时
- 配置变更时缓存可能不会立即更新
- 没有缓存大小限制，可能导致内存增长

#### 4. **统计更新延迟风险**
```go
// upload_core.go:190-200
if statsUpdater != nil {
    if prereqs.PathResult != nil {
        l1 := prereqs.PathResult.L1
        l2 := prereqs.PathResult.L2
        statsUpdater.AddDirStats(l1, l2, 1, 1)
    }
}
```
**风险**: 
- 统计数据每10分钟批量更新，服务重启可能导致数据丢失
- 没有事务保证，统计可能不准确

#### 5. **文件大小验证逻辑问题**
```go
// upload_core.go:662-800
// 检查是否已达到限制。尝试多读取一个字节。
oneMoreByte := make([]byte, 1)
n, _ := reader.Read(oneMoreByte)
if n > 0 {
    cleanup()
    return nil, nil, nil, fmt.Errorf("file size exceeds limit: %w", ErrFileTooLarge)
}
```
**问题**: 
- 忽略读取错误(`_`)，可能掩盖真实的IO错误
- 这种检测方式不够可靠，可能误判

#### 6. **并发竞争条件**
```go
// upload_chunked.go:428-435
state.mu.Lock()
if state.isFinalized {
    state.mu.Unlock()
    return nil, fmt.Errorf("upload has already been finalized")
}
state.isFinalized = true
state.mu.Unlock()
```
**风险**: 在检查和设置之间可能有其他goroutine介入

#### 7. **路径遍历攻击防护不完整**
```go
// upload_directory.go:178-213
func validateRelativePath(path string) error {
    // 检查路径遍历
    if strings.Contains(path, "..") {
        return fmt.Errorf("path traversal not allowed: %s", path)
    }
}
```
**问题**: 
- 只检查`..`，没有检查其他路径遍历技术
- 没有检查绝对路径的完整逻辑

#### 8. **错误处理不一致**
```go
// upload_core.go:560-595
func rollbackWrittenFiles(ctx context.Context, writtenPaths []WrittenPath, _ map[string]levelStore.S3ProviderConfig, s3ClientMap map[string]s3API, opts WriteOptions) []error {
    var wg sync.WaitGroup
    var mu sync.Mutex
    var rollbackErrors []error
    
    for _, path := range writtenPaths {
        wg.Add(1)
        go func(path WrittenPath) {
            defer wg.Done()
            var err error
            // ... 删除逻辑
            if err != nil {
                mu.Lock()
                rollbackErrors = append(rollbackErrors, err)
                mu.Unlock()
            }
        }(path)
    }
    wg.Wait()
    return rollbackErrors
}
```
**问题**: 
- 并发删除时，如果某个删除失败，不会停止其他删除操作
- 没有重试机制

#### 9. **配置验证不足**
```go
// upload_core.go:432-460
func getS3ProviderMap(_ context.Context, storage []levelStore.StorageConfig) (map[string]levelStore.S3ProviderConfig, error) {
    // 没有验证S3配置的有效性
}
```
**问题**: 没有验证S3配置的完整性和有效性

#### 10. **资源清理不完整**
```go
// upload_chunked.go:453
defer func() { _ = fileReader.Close() }() // Ensure the file reader is always closed
```
**问题**: 
- 使用`_`忽略关闭错误
- 没有检查文件是否成功关闭

### 🔧 建议的改进措施

#### 1. **内存管理改进**
```go
// 建议使用defer确保清理
func uploadWithCleanup() {
    tempFile, cleanup := createTempFile()
    defer cleanup() // 确保总是被调用
}
```

#### 2. **状态持久化**
```go
// 建议添加Redis或数据库持久化
type PersistentChunkedUploadState struct {
    // 添加持久化字段
    PersistedAt time.Time
    // ...
}
```

#### 3. **配置验证增强**
```go
func validateS3Config(config levelStore.S3ProviderConfig) error {
    // 验证所有必需字段
    if config.Endpoint == "" {
        return errors.New("S3 endpoint is required")
    }
    // ...
}
```

#### 4. **错误处理改进**
```go
// 建议添加更详细的错误类型
type UploadError struct {
    Type    string
    Message string
    Details map[string]interface{}
}
```

#### 5. **监控和日志增强**
```go
// 建议添加性能监控
func uploadWithMetrics() {
    start := time.Now()
    defer func() {
        metrics.RecordUploadDuration(time.Since(start))
    }()
}
```

### �� 总体评估

**风险等级**: 中等
- 代码质量良好，但存在一些生产环境风险
- 主要风险集中在资源管理和状态持久化
- 建议在生产环境部署前解决上述问题

**建议优先级**:
1. 🔴 高优先级: 解决内存泄漏和资源清理问题
2. �� 中优先级: 改进状态管理和错误处理
3. 🟢 低优先级: 增强监控和日志

除了UT外，这些是主要的风险点和需要关注的问题。建议在部署到生产环境前优先解决高优先级问题。

---

